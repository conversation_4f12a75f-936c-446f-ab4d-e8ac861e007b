'use client';

import React, { createContext, useContext, useState, useCallback, useMemo, useEffect, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { getMySchool } from '@/actions/school.action';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
}

export interface SchoolData {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  registeredNumber?: string;
}

export interface BrandingData {
  primaryColor?: string;
  theme?: string;
}

export interface ProfileData {
  bio?: string;
  specialization?: string;
  experience?: string;
}

export interface OnboardingState {
  currentStep: string;
  steps: OnboardingStep[];
  schoolData: SchoolData | null;
  brandingData: BrandingData | null;
  profileData: ProfileData | null;
  isLoading: boolean;
  error: string | null;
  // New fields for school data management
  schoolId: string | null;
  schoolFetchStatus: 'idle' | 'loading' | 'success' | 'error';
  schoolFetchError: string | null;
  hasFullSchoolData: boolean;
  branchData: any | null; // For future use
}

export interface OnboardingContextType {
  state: OnboardingState;
  isInitialized: boolean;
  setCurrentStep: (step: string) => void;
  setSchoolData: (data: SchoolData) => void;
  setBrandingData: (data: BrandingData) => void;
  setProfileData: (data: ProfileData) => void;
  markStepCompleted: (stepId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetOnboarding: () => void;
  canNavigateToStep: (stepId: string) => boolean;
  getNextStep: () => string | null;
  getPreviousStep: () => string | null;
  // New methods for school data management
  fetchSchoolData: () => Promise<void>;
  setSchoolFetchStatus: (status: 'idle' | 'loading' | 'success' | 'error') => void;
  setSchoolFetchError: (error: string | null) => void;
  retrySchoolFetch: () => Promise<void>;
  determineNextStepBasedOnState: () => string | null;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'school-details',
    title: 'School Details',
    description: 'Set up your school information',
    isCompleted: false,
  },
  {
    id: 'branding',
    title: 'Branding',
    description: 'Customize your school appearance',
    isCompleted: false,
  },
  {
    id: 'profile',
    title: 'Profile',
    description: 'Complete your teacher profile',
    isCompleted: false,
  },
  {
    id: 'complete',
    title: 'Complete',
    description: 'Finish setup',
    isCompleted: false,
  },
];

const initialState: OnboardingState = {
  currentStep: 'school-details',
  steps: ONBOARDING_STEPS,
  schoolData: null,
  brandingData: null,
  profileData: null,
  isLoading: false,
  error: null,
  // New fields for school data management
  schoolId: null,
  schoolFetchStatus: 'idle',
  schoolFetchError: null,
  hasFullSchoolData: false,
  branchData: null,
};

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const { data: session } = useSession();
  const [state, setState] = useState<OnboardingState>(initialState);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize onboarding state based on session data
  useEffect(() => {
    if (session?.user && !isInitialized) {
      const hasSchoolId = session.user.schoolId;
      const hasFullSchoolData = session.user.schoolId && session.user.school;

      // Update state with schoolId information
      setState(prev => ({
        ...prev,
        schoolId: hasSchoolId,
        hasFullSchoolData: !!hasFullSchoolData,
      }));

      if (hasFullSchoolData) {
        // User already has complete school data, mark school-details as completed
        // and set current step to branding
        setState(prev => ({
          ...prev,
          currentStep: 'branding',
          steps: prev.steps.map(step =>
            step.id === 'school-details' ? { ...step, isCompleted: true } : step
          ),
          schoolData: {
            name: session.user.school?.name || '',
            address: session.user.school?.address || '',
            phone: session.user.school?.phoneNumber || '',
            registeredNumber: session.user.school?.registeredNumber || '',
            email: session.user.school?.email || '',
          },
          schoolFetchStatus: 'success',
        }));
      } else if (hasSchoolId && !session.user.school) {
        // User has schoolId but no school object - need to fetch school data
        console.log('User has schoolId but no school object, will fetch school data');
        setState(prev => ({
          ...prev,
          schoolFetchStatus: 'idle', // Will be triggered by SchoolDetailsStep
        }));
      }

      setIsInitialized(true);
    }
  }, [session, isInitialized]);

  const setCurrentStep = useCallback((step: string) => {
    setState(prev => ({
      ...prev,
      currentStep: step,
      error: null,
    }));
  }, []);

  const setSchoolData = useCallback((data: SchoolData) => {
    setState(prev => ({
      ...prev,
      schoolData: data,
    }));
  }, []);

  const setBrandingData = useCallback((data: BrandingData) => {
    setState(prev => ({
      ...prev,
      brandingData: data,
    }));
  }, []);

  const setProfileData = useCallback((data: ProfileData) => {
    setState(prev => ({
      ...prev,
      profileData: data,
    }));
  }, []);

  const markStepCompleted = useCallback((stepId: string) => {
    setState(prev => ({
      ...prev,
      steps: prev.steps.map(step =>
        step.id === stepId ? { ...step, isCompleted: true } : step
      ),
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
    }));
  }, []);

  const setSchoolFetchStatus = useCallback((status: 'idle' | 'loading' | 'success' | 'error') => {
    setState(prev => ({
      ...prev,
      schoolFetchStatus: status,
    }));
  }, []);

  const setSchoolFetchError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      schoolFetchError: error,
    }));
  }, []);

  const fetchSchoolData = useCallback(async () => {
    if (!session?.user?.schoolId) {
      setSchoolFetchError('No school ID found in session');
      return;
    }

    setSchoolFetchStatus('loading');
    setSchoolFetchError(null);

    try {
      const response = await getMySchool();

      if (response.status === 'success' && response.data) {
        // Update school data in context
        setSchoolData({
          name: response.data.name || '',
          address: response.data.address || '',
          phone: response.data.phoneNumber || '',
          registeredNumber: response.data.registeredNumber || '',
          email: response.data.email || '',
        });

        setState(prev => ({
          ...prev,
          schoolFetchStatus: 'success',
          hasFullSchoolData: true,
          schoolId: session.user.schoolId,
        }));

        // Mark school details step as completed
        markStepCompleted('school-details');
      } else {
        throw new Error('Failed to fetch school data');
      }
    } catch (error: any) {
      console.error('Error fetching school data:', error);
      setSchoolFetchStatus('error');
      setSchoolFetchError(error.message || 'Failed to fetch school data');
    }
  }, [session?.user?.schoolId, setSchoolData, markStepCompleted, setSchoolFetchError, setSchoolFetchStatus]);

  const retrySchoolFetch = useCallback(async () => {
    await fetchSchoolData();
  }, [fetchSchoolData]);

  const resetOnboarding = useCallback(() => {
    setState(initialState);
  }, []);

  const canNavigateToStep = useCallback((stepId: string) => {
    const stepIndex = state.steps.findIndex(step => step.id === stepId);
    const currentStepIndex = state.steps.findIndex(step => step.id === state.currentStep);

    // Can navigate to current step or any previous completed step
    if (stepIndex <= currentStepIndex) {
      return true;
    }

    // Can navigate to next step if current step is completed
    if (stepIndex === currentStepIndex + 1) {
      const currentStep = state.steps[currentStepIndex];

      // Special handling for school-details step
      if (currentStep.id === 'school-details') {
        // Can advance if:
        // 1. Step is marked as completed, OR
        // 2. School data fetch is successful, OR
        // 3. User has full school data already
        return currentStep.isCompleted ||
               state.schoolFetchStatus === 'success' ||
               state.hasFullSchoolData;
      }

      return currentStep.isCompleted;
    }

    return false;
  }, [state.steps, state.currentStep, state.schoolFetchStatus, state.hasFullSchoolData]);

  const getNextStep = useCallback((): string | null => {
    const currentIndex = state.steps.findIndex(step => step.id === state.currentStep);
    const currentStep = state.steps[currentIndex];

    // Special logic for school-details step
    if (currentStep?.id === 'school-details') {
      // If school data is loading, don't advance yet
      if (state.schoolFetchStatus === 'loading') {
        return null;
      }

      // If school data fetch failed, stay on current step
      if (state.schoolFetchStatus === 'error') {
        return null;
      }

      // If we have school data (either from session or fetched), advance to branding
      if (state.hasFullSchoolData || state.schoolFetchStatus === 'success') {
        // Check if we have branch data to determine next step
        if (state.branchData) {
          // If we have branch data, skip branding and go to profile
          return 'profile';
        } else {
          // No branch data, go to branding step
          return 'branding';
        }
      }
    }

    // Default behavior: go to next step in sequence
    if (currentIndex < state.steps.length - 1) {
      return state.steps[currentIndex + 1].id;
    }
    return null;
  }, [state.steps, state.currentStep, state.schoolFetchStatus, state.hasFullSchoolData, state.branchData]);

  const getPreviousStep = useCallback((): string | null => {
    const currentIndex = state.steps.findIndex(step => step.id === state.currentStep);
    if (currentIndex > 0) {
      return state.steps[currentIndex - 1].id;
    }
    return null;
  }, [state.steps, state.currentStep]);

  // Intelligent step transition based on current state
  const determineNextStepBasedOnState = useCallback((): string | null => {
    // If user has schoolId but no school object, and fetch is not in progress
    if (state.schoolId && !state.hasFullSchoolData && state.schoolFetchStatus === 'idle') {
      // Stay on school-details step and trigger fetch
      return 'school-details';
    }

    // If school data is loading, stay on current step
    if (state.schoolFetchStatus === 'loading') {
      return state.currentStep;
    }

    // If school data fetch failed, stay on school-details step
    if (state.schoolFetchStatus === 'error') {
      return 'school-details';
    }

    // If we have complete school data
    if (state.hasFullSchoolData || state.schoolFetchStatus === 'success') {
      // Check if we have branch data
      if (state.branchData) {
        // Has both school and branch data - go to profile completion
        return 'profile';
      } else {
        // Has school data but no branch data - go to branding
        return 'branding';
      }
    }

    // Default: start with school details
    return 'school-details';
  }, [state.schoolId, state.hasFullSchoolData, state.schoolFetchStatus, state.branchData, state.currentStep]);

  const contextValue = useMemo<OnboardingContextType>(() => ({
    state,
    isInitialized,
    setCurrentStep,
    setSchoolData,
    setBrandingData,
    setProfileData,
    markStepCompleted,
    setLoading,
    setError,
    resetOnboarding,
    canNavigateToStep,
    getNextStep,
    getPreviousStep,
    fetchSchoolData,
    setSchoolFetchStatus,
    setSchoolFetchError,
    retrySchoolFetch,
    determineNextStepBasedOnState,
  }), [
    state,
    isInitialized,
    setCurrentStep,
    setSchoolData,
    setBrandingData,
    setProfileData,
    markStepCompleted,
    setLoading,
    setError,
    resetOnboarding,
    canNavigateToStep,
    getNextStep,
    getPreviousStep,
    fetchSchoolData,
    setSchoolFetchStatus,
    setSchoolFetchError,
    retrySchoolFetch,
    determineNextStepBasedOnState,
  ]);

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
